package com.joyadata.dsc.datasource.controller;

import com.joyadata.controller.BaseController;
import com.joyadata.dsc.datasource.model.datasoure.DatasourceAuthorization;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 数据源授权
 */
@RestController
@CrossOrigin
@RequestMapping("/datasourceAuthorization")
public class DatasourceAuthorizationController extends BaseController<DatasourceAuthorization> {

}
