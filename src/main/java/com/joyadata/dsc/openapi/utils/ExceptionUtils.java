package com.joyadata.dsc.openapi.utils;

import com.joyadata.dsc.openapi.enums.DataSourceErrorCode;
import com.joyadata.dsc.openapi.response.OpenApiResponse;
import com.joyadata.exception.AppErrorException;
import lombok.extern.slf4j.Slf4j;

import java.util.function.Supplier;

@Slf4j
public class ExceptionUtils {

    public static void throwException(DataSourceErrorCode errorCodeEnum) {
        throwException(errorCodeEnum.getCode(), errorCodeEnum.getMessageZh());
    }

    public static void throwException(Integer errorCode, String message) {
        final AppErrorException appErrorException = new AppErrorException();
        appErrorException.setCode(errorCode);
        appErrorException.setMessage(message);
        throw appErrorException;
    }

    /**
     * 统一异常处理方法
     */
    public static OpenApiResponse handleRequest(Supplier<Object> operation) {
        try {
            return OpenApiResponse.success(operation.get());
        } catch (AppErrorException e) {
            log.error("OpenApi AppErrorException", e);
            return OpenApiResponse.error(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error("OpenApi Exception", e);
            return OpenApiResponse.error(99999, e.getMessage());
        }
    }
}
