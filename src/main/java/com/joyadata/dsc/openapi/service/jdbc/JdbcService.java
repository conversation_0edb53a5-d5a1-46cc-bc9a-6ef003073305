package com.joyadata.dsc.openapi.service.jdbc;

import com.dtstack.dtcenter.loader.dto.ColumnMetaDTO;
import com.joyadata.dsc.openapi.response.ResultSet;
import com.joyadata.dsc.openapi.response.TableAndView;

import java.util.List;

public interface JdbcService {

    /**
     * 测试数据库连接
     */
    boolean checkJdbcConnection(String datasourceId);

    /**
     * 执行query，拿到返回结果
     */
    ResultSet executeQuery(String datasourceId, String sql);

    /**
     * 执行sql，没有返回结果
     */
    boolean executeSql(String datasourceId, String sql);

    /**
     * 执行sql，判断表是否存在
     */
    boolean isTableExists(String datasourceId, String dbName,String schemaName,String tableName, boolean isView);

    /**
     * 执行sql获取数据总数
     */
    long count(String datasourceId, String sql);

    /**
     * 获取数据源下表和视图
     */
    List<TableAndView> getTableAndViewList(String datasourceId, String dbName, String schemaName, String tablePattern, boolean needView);

    /**
     * 获取表列信息
     */
    List<ColumnMetaDTO> getTableColumn(String datasourceId, String dbName, String schemaName, String tableName, boolean isView);

    /**
     * 预览数据
     * */
    ResultSet preview(String datasourceId, String dbName, String schemaName, String tableName, List<String> columns,int limit,String whereCondition);






}
