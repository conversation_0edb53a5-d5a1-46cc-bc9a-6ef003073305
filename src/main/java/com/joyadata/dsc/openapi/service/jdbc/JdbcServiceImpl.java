package com.joyadata.dsc.openapi.service.jdbc;

import com.dtstack.dtcenter.loader.dto.ColumnMetaDTO;
import com.dtstack.dtcenter.loader.dto.TableViewDTO;
import com.joyadata.dsc.openapi.response.ResultSet;
import com.joyadata.dsc.openapi.response.TableAndView;
import com.joyadata.dsc.openapi.service.AbstractDataSourceClientService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import static com.joyadata.dsc.openapi.constants.CommonConstants.TABLE;
import static com.joyadata.dsc.openapi.constants.JdbcConstants.COUNT_COLUMN;
import static com.joyadata.dsc.openapi.constants.JdbcConstants.COUNT_SQL;
import static com.joyadata.dsc.openapi.utils.JdbcUtils.getResultSet;
import static java.util.Objects.requireNonNull;

@Slf4j
@Service
public class JdbcServiceImpl extends AbstractDataSourceClientService implements JdbcService {

    @Override
    public boolean checkJdbcConnection(String datasourceId) {
        return super.checkJdbcConnection(datasourceId);
    }

    @Override
    public ResultSet executeQuery(String datasourceId, String sql) {
        log.info("datasourceId:{}, executeSql is :{}", datasourceId, sql);
        requireNonNull(sql);
        return executeSqlWithConnection(datasourceId, sql, (stmt, finalSql) -> {
            try (java.sql.ResultSet rs = stmt.executeQuery(finalSql)) {
                return getResultSet(rs);
            }
        });
    }

    @Override
    public boolean executeSql(String datasourceId, String sql) {
        log.info("datasourceId:{}, executeSql is :{}", datasourceId, sql);
        requireNonNull(sql);
        return executeSqlWithConnection(datasourceId, sql, (stmt, finalSql) -> {
            try{
                stmt.execute(finalSql);
                return true;
            }catch (Exception e) {
                return false;
            }
        });
    }

    @Override
    public boolean isTableExists(String datasourceId, String dbName, String schemaName, String tableName, boolean isView) {
        log.info("datasourceId:{},dbName:{},schemaName:{},tableName:{},isView:{}", datasourceId, dbName, schemaName, tableName, isView);
        return executeClientOperation(datasourceId,
                builder -> builder.dbName(dbName).schema(schemaName).tableName(tableName).view(isView),
                triple -> triple.getLeft().isTableExistsInDatabase(triple.getMiddle(), triple.getRight()));
    }

    @Override
    public long count(String datasourceId, String sql) {
        log.info("datasourceId:{}, executeSql is :{}", datasourceId, sql);
        requireNonNull(sql);
        String countSql = String.format(COUNT_SQL, sql);
        log.info("Count SQL: {}", countSql);
        return executeSqlWithConnection(datasourceId, countSql, (stmt, finalSql) -> {
            try (java.sql.ResultSet rs = stmt.executeQuery(finalSql)) {
                if (rs.next()) {
                    return rs.getLong(COUNT_COLUMN);
                }
                return 0L;
            }
        });
    }

    @Override
    public List<TableAndView> getTableAndViewList(String datasourceId, String dbName, String schemaName, String tablePattern, boolean needView) {
        log.info("datasourceId:{},dbName:{},schemaName:{},tablePattern:{},needView:{}", datasourceId, dbName, schemaName, tablePattern, needView);
        return executeClientOperation(datasourceId,
                builder -> builder.dbName(dbName).schema(schemaName).tableNamePattern(tablePattern).view(needView),
                triple -> {
                    List<TableViewDTO> tableAndViewList = triple.getLeft().getTableAndViewList(triple.getMiddle(), triple.getRight());
                    // todo 后面要优化成直接获取列表，省去遍历
                    return tableAndViewList.stream()
                            .map(dto -> new TableAndView(dto.getName(),!StringUtils.equalsIgnoreCase(dto.getType(),TABLE)))
                            .collect(Collectors.toList());
                });
    }

    @Override
    public List<ColumnMetaDTO> getTableColumn(String datasourceId, String dbName, String schemaName, String tableName, boolean isView) {
        log.info("datasourceId:{},dbName:{},schemaName:{},tableName:{},isView:{}", datasourceId, dbName, schemaName, tableName, isView);
        return executeClientOperation(datasourceId,
                builder -> builder.dbName(dbName).schema(schemaName).tableName(tableName).view(isView),
                triple -> triple.getLeft().getColumnMetaData(triple.getMiddle(), triple.getRight()));
    }

    @Override
    public ResultSet preview(String datasourceId, String dbName, String schemaName, String tableName, List<String> columns, int limit, String whereCondition) {
        log.info("datasourceId:{},dbName:{},schemaName:{},tableName:{},columns:{},limit:{},whereCondition:{}", datasourceId, dbName, schemaName, tableName, columns, limit, whereCondition);
        return executeClientOperation(datasourceId,
                builder -> builder.dbName(dbName).schema(schemaName).tableName(tableName).previewNum(limit).whereRelation(whereCondition),
                triple -> {
                    final List<List<Object>> preview = triple.getLeft().getPreview(triple.getMiddle(), triple.getRight());
                    final List<Object> objects = preview.get(0);
                    preview.remove(0);
                    final List<String> metaData = objects.stream().map(Object::toString).collect(Collectors.toList());
                    if (CollectionUtils.isEmpty(columns)) {
                        return new ResultSet(metaData,preview);
                    }else{
                        // todo 后面要优化成直接从sql中获取
                        // 比对columns和metaData， 处理二维数组，只返回特定列
                        List<List<Object>> result = new ArrayList<>();
                        for (List<Object> row : preview) {
                            List<Object> newRow = new ArrayList<>();
                            for (String column : columns) {
                                int index = metaData.indexOf(column);
                                if (index != -1) {
                                    newRow.add(row.get(index));
                                }
                            }
                            result.add(newRow);
                        }
                        return new ResultSet(columns,result);
                    }
                });
    }


}
