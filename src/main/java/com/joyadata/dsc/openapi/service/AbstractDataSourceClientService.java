package com.joyadata.dsc.openapi.service;


import com.dsg.database.datasource.dto.DatasourceDTO;
import com.dsg.database.datasource.enums.DataSourceTypeEnum;
import com.dsg.database.datasource.enums.SourceDTOType;
import com.dtstack.dtcenter.loader.client.ClientCache;
import com.dtstack.dtcenter.loader.client.IClient;
import com.dtstack.dtcenter.loader.client.IKafka;
import com.dtstack.dtcenter.loader.client.IKerberos;
import com.dtstack.dtcenter.loader.dto.SqlQueryDTO;
import com.dtstack.dtcenter.loader.dto.source.ISourceDTO;
import com.google.common.collect.Maps;
import com.joyadata.dsc.datasource.service.DatasourceInfoService;
import com.joyadata.dsc.openapi.utils.JdbcUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Triple;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.sql.Connection;
import java.sql.Statement;
import java.util.Map;
import java.util.function.Function;
import java.util.function.Supplier;

import static com.joyadata.dsc.openapi.enums.DataSourceErrorCode.DATASOURCE_CONNECT_FAILED;
import static com.joyadata.dsc.openapi.enums.DataSourceErrorCode.DATASOURCE_NOT_FOUND;
import static com.joyadata.dsc.openapi.enums.DataSourceErrorCode.SQL_INJECTION_RISK;
import static com.joyadata.dsc.openapi.utils.ExceptionUtils.throwException;

@Slf4j
@Service
public class AbstractDataSourceClientService {

    @Autowired
    private DatasourceInfoService datasourceInfoService;

    protected boolean checkJdbcConnection(String datasourceId) {
        log.info("datasourceId:{}",datasourceId);
        Triple<IClient, ISourceDTO,SqlQueryDTO> iClientISourceDTOTriple = iClient(datasourceId,
                () -> SqlQueryDTO.builder().build());
        return iClientISourceDTOTriple.getLeft().testCon(iClientISourceDTOTriple.getMiddle());
    }

    /**
     * 通用的客户端操作方法
     */
    protected <T> T executeClientOperation(String datasourceId,
                                         Function<SqlQueryDTO.SqlQueryDTOBuilder, SqlQueryDTO.SqlQueryDTOBuilder> queryBuilder,
                                         Function<Triple<IClient, ISourceDTO, SqlQueryDTO>, T> operation) {
        final Triple<IClient, ISourceDTO, SqlQueryDTO> triple = iClient(datasourceId,
                () -> queryBuilder.apply(SqlQueryDTO.builder()).build());
        return operation.apply(triple);
    }

    protected Connection getConnection(String datasourceId) {
        // 这里获取的连接已经是从连接池里获取的
        Triple<IClient, ISourceDTO,SqlQueryDTO> iClientISourceDTOTriple = iClient(datasourceId,
                () -> SqlQueryDTO.builder().build());
        return iClientISourceDTOTriple.getLeft().getCon(iClientISourceDTOTriple.getMiddle());
    }

    protected <T> Triple<T, ISourceDTO, SqlQueryDTO> getDatasourceTriple(
            String datasourceId,
            Supplier<SqlQueryDTO> getSqlQueryDTO,
            Function<DataSourceTypeEnum, T> clientProvider) {

        // 查询数据源信息
        DatasourceDTO datasourceDTO = null;
        try {
            datasourceDTO = datasourceInfoService.getDatasourceInfoById(datasourceId);
        } catch (Exception e) {
            throwException(99999, e.getMessage());
        }
        if (datasourceDTO == null) {
            throwException(DATASOURCE_NOT_FOUND);
        }
        // 获取数据源类型
        DataSourceTypeEnum typeEnum = DataSourceTypeEnum.getDbEnum(datasourceDTO.getDataType().toLowerCase());
        // Kerberos 配置处理
        Map<String, Object> confMap = datasourceDTO.getKerberosConfig();
        String localKerberosPath = datasourceDTO.getDatasourceInfoIdInKerberosPath();
        Map<String, Object> tempConfMap = null;
        if (MapUtils.isNotEmpty(confMap)) {
            tempConfMap = Maps.newHashMap(confMap);
            if (StringUtils.isNotEmpty(localKerberosPath)) {
                IKerberos kerberos = ClientCache.getKerberos(typeEnum.getVal());
                kerberos.prepareKerberosForConnect(tempConfMap, localKerberosPath);
            }
        }
        // 构建 ISourceDTO 和 SqlQueryDTO
        ISourceDTO sourceDTO = SourceDTOType.getSourceDTO(
                datasourceDTO.getDataJsonMap(),
                typeEnum.getVal(),
                tempConfMap,
                Maps.newHashMap()
        );
        SqlQueryDTO sqlQueryDTO = getSqlQueryDTO.get();
        T client = clientProvider.apply(typeEnum);
        Triple<T, ISourceDTO, SqlQueryDTO> result = Triple.of(client, sourceDTO, sqlQueryDTO);
        return result;

    }


    protected Triple<IClient, ISourceDTO, SqlQueryDTO> iClient(String datasourceId, Supplier<SqlQueryDTO> getSqlQueryDTO) {
        return getDatasourceTriple(datasourceId, getSqlQueryDTO, typeEnum -> ClientCache.getClient(typeEnum.getVal()));
    }

    protected Triple<IKafka, ISourceDTO, SqlQueryDTO> iKafka(String datasourceId, Supplier<SqlQueryDTO> getSqlQueryDTO) {
        return getDatasourceTriple(datasourceId, getSqlQueryDTO, typeEnum -> ClientCache.getKafka(typeEnum.getVal()));
    }


    // 检查sql
    protected void checkSql(String sql) {
        if (!JdbcUtils.isValidSql(sql)) {
            throwException(SQL_INJECTION_RISK);
        }
    }

    /**
     * 执行SQL的通用方法
     */
    protected  <T> T executeSqlWithConnection(String datasourceId, String sql, SqlExecutor<T> executor) {
        // 检查连接
        if (!checkJdbcConnection(datasourceId)) {
            throwException(DATASOURCE_CONNECT_FAILED);
        }
        // 校验sql，防止sql注入
        checkSql(sql);
        try (Connection connection = getConnection(datasourceId);
             Statement stmt = connection.createStatement()) {
            return executor.execute(stmt, sql);
        } catch (Exception e) {
            log.error("SQL execution error", e);
            throwException(99999, e.getMessage());
            return null;
        }
    }

    /**
     * SQL执行器函数式接口
     */
    @FunctionalInterface
    protected interface SqlExecutor<T> {
        T execute(Statement stmt, String sql) throws Exception;
    }
}
