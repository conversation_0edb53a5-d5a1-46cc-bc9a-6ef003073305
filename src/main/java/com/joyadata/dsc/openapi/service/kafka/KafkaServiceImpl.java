package com.joyadata.dsc.openapi.service.kafka;

import com.dtstack.dtcenter.loader.client.IKafka;
import com.dtstack.dtcenter.loader.dto.KafkaPartitionDTO;
import com.dtstack.dtcenter.loader.dto.SqlQueryDTO;
import com.dtstack.dtcenter.loader.dto.source.ISourceDTO;
import com.joyadata.dsc.openapi.service.AbstractDataSourceClientService;
import org.apache.commons.lang3.tuple.Triple;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class KafkaServiceImpl extends AbstractDataSourceClientService implements KafkaService {
    @Override
    public List<String> getTopicList(String datasourceId) {
        final Triple<IKafka, ISourceDTO, SqlQueryDTO> triple = iKafka(datasourceId, () -> SqlQueryDTO.builder().build());
        return triple.getLeft().getTopicList(triple.getMiddle());
    }

    @Override
    public List<KafkaPartitionDTO> getTopicPartition(String datasourceId, String topic) {
        final Triple<IKafka, ISourceDTO, SqlQueryDTO> triple = iKafka(datasourceId, () -> SqlQueryDTO.builder().build());
        return triple.getLeft().getTopicPartitions(triple.getMiddle(), topic);
    }
}
