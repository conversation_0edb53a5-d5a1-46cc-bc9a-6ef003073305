package com.joyadata.dsc.openapi.controller;

import cn.hutool.json.JSONUtil;
import com.joyadata.dsc.openapi.response.OpenApiResponse;
import com.joyadata.dsc.openapi.service.jdbc.JdbcService;
import com.joyadata.exception.AppErrorException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.Arrays;
import java.util.List;
import java.util.function.Supplier;

import static com.joyadata.dsc.openapi.utils.ExceptionUtils.handleRequest;

/**
 * 对其他服务开放的Http接口（jdbc相关），由datasource-sdk包调用
 * */
@RestController
@RequestMapping("/jdbc")
public class JdbcController {

    @Autowired
    private JdbcService jdbcService;


    /**测试数据库连接*/
    @GetMapping("/check/{datasourceId}")
    public OpenApiResponse checkJdbcConnection(@PathVariable String datasourceId) {
        return handleRequest(() -> jdbcService.checkJdbcConnection(datasourceId));
    }

    /**执行query，拿到返回结果*/
    @PostMapping("/query/{datasourceId}")
    public OpenApiResponse executeQuery(@PathVariable String datasourceId, @RequestBody List<String> sql) {
        return handleRequest(() -> jdbcService.executeQuery(datasourceId, sql.get(0)));
    }

    /**执行sql，没有返回结果*/
    @PostMapping("/execute/{datasourceId}")
    public OpenApiResponse executeSql(@PathVariable String datasourceId, @RequestBody List<String> sql) {
        return handleRequest(() -> jdbcService.executeSql(datasourceId, sql.get(0)));
    }

    /**判断表是否存在*/
    @GetMapping("/tableExists/{datasourceId}")
    public OpenApiResponse isTableExists(@PathVariable String datasourceId,
                                           @RequestParam String dbName,
                                           @RequestParam(required = false) String schemaName,
                                           @RequestParam String tableName,
                                           @RequestParam(required = false ,defaultValue = "false") boolean isView) {
        return handleRequest(() -> jdbcService.isTableExists(datasourceId, dbName, schemaName, tableName, isView));
    }

    /**执行sql获取数据总数*/
    @PostMapping("/count/{datasourceId}")
    public OpenApiResponse count(@PathVariable String datasourceId, @RequestBody List<String> sql) {
        return handleRequest(() -> jdbcService.count(datasourceId, sql.get(0)));
    }

    /**获取数据源下表和视图*/
    @GetMapping("/tableAndViewList/{datasourceId}")
    public OpenApiResponse getTableAndViewList(@PathVariable String datasourceId,
                                               @RequestParam String dbName,
                                               @RequestParam(required = false) String schemaName,
                                               @RequestParam(required = false) String tablePattern,
                                               @RequestParam(required = false ,defaultValue = "false") boolean needView) {
        return handleRequest(() -> jdbcService.getTableAndViewList(datasourceId, dbName, schemaName, tablePattern, needView));
    }
    /**获取表列信息*/
    @GetMapping("/tableColumn/{datasourceId}")
    public OpenApiResponse getTableColumn(@PathVariable String datasourceId,
                                           @RequestParam String dbName,
                                           @RequestParam(required = false) String schemaName,
                                           @RequestParam String tableName,
                                           @RequestParam(required = false,defaultValue = "false") boolean isView) {
        return handleRequest(() -> jdbcService.getTableColumn(datasourceId, dbName, schemaName, tableName, isView));
    }

    /**预览数据*/
    @PostMapping("/preview/{datasourceId}")
    public OpenApiResponse preview(@PathVariable String datasourceId,
                                   @RequestParam String dbName,
                                   @RequestParam(required = false) String schemaName,
                                   @RequestParam String tableName,
                                   @RequestBody (required = false) List<String> columns,
                                   @RequestParam(required = false,defaultValue = "100") int limit,
                                   @RequestParam(required = false) String whereCondition) {
        return handleRequest(() -> jdbcService.preview(datasourceId, dbName, schemaName, tableName, columns, limit, whereCondition));
    }

}
