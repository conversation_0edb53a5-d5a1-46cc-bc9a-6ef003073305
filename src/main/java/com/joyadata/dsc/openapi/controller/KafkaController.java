package com.joyadata.dsc.openapi.controller;


import com.joyadata.dsc.openapi.response.OpenApiResponse;
import com.joyadata.dsc.openapi.service.kafka.KafkaService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import static com.joyadata.dsc.openapi.utils.ExceptionUtils.handleRequest;

/**
 * 对其他服务开放的Http接口（kafka相关），由datasource-sdk包调用
 * */
@RestController
@RequestMapping("/kafka")
public class KafkaController {

    @Autowired
    private KafkaService kafkaService;

    /**获取topIc list*/
    @GetMapping("/topicList/{datasourceId}")
    public OpenApiResponse getTopicList(@PathVariable String datasourceId) {
        return handleRequest(() -> kafkaService.getTopicList(datasourceId));
    }

    /**获取kafka指定topic下的分区信息*/
    @GetMapping("/topicPartition/{datasourceId}")
    public OpenApiResponse getTopicPartition(@PathVariable String datasourceId, @RequestParam String topic) {
        return handleRequest(() -> kafkaService.getTopicPartition(datasourceId, topic));
    }
}
